#!/usr/bin/env python3
"""
Simple test server to view the HTML file with our updated CSS
"""
from flask import Flask, render_template, send_from_directory
import os

app = Flask(__name__, template_folder='templates', static_folder='static')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    print("Starting test server...")
    print("Open http://localhost:5001 to view the updated greeting box")
    app.run(debug=True, port=5001, host='0.0.0.0')
